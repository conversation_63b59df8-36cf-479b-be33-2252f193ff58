export interface ShowcaseItem {
  id: string;
  name: string;
  description: string;
  url: string;
  image: string;
  category: string;
  featured?: boolean;
  components?: string[];
}

export const showcaseData: ShowcaseItem[] = [
  {
    id: "mvpblocks-main",
    name: "MVPBlocks",
    description: "The official MVPBlocks website showcasing our complete component library with beautiful hero sections, feature grids, and interactive elements.",
    url: "https://blocks.mvp-subha.me",
    image: "/banner.webp",
    category: "Component Library",
    featured: true,
    components: ["hero", "features", "gallery", "testimonials", "cta", "footer"]
  },
  {
    id: "saas-dashboard",
    name: "SaaS Dashboard Pro",
    description: "A modern SaaS dashboard built with MVPBlocks components featuring analytics charts, user management, and responsive design.",
    url: "https://blocks.mvp-subha.me",
    image: "/assets/lunexa-db.png",
    category: "SaaS",
    components: ["dashboard", "charts", "tables", "forms", "navigation"]
  },
  {
    id: "ecommerce-store",
    name: "Modern Store",
    description: "An elegant e-commerce platform showcasing product grids, shopping cart functionality, and checkout flows using MVPBlocks.",
    url: "https://blocks.mvp-subha.me",
    image: "/assets/card-carousel/1.webp",
    category: "E-commerce",
    components: ["product-grid", "cart", "checkout", "hero", "testimonials"]
  },
  {
    id: "portfolio-site",
    name: "Creative Portfolio",
    description: "A stunning portfolio website for designers and developers featuring project showcases and contact forms.",
    url: "https://blocks.mvp-subha.me",
    image: "/assets/card-carousel/2.webp",
    category: "Portfolio",
    components: ["hero", "gallery", "contact", "about", "testimonials"]
  },
  {
    id: "landing-page",
    name: "Product Launch",
    description: "A high-converting landing page for product launches with compelling CTAs, feature highlights, and social proof.",
    url: "https://blocks.mvp-subha.me",
    image: "/assets/card-carousel/3.webp",
    category: "Landing Page",
    components: ["hero", "features", "pricing", "testimonials", "cta"]
  },
  {
    id: "blog-platform",
    name: "Tech Blog Hub",
    description: "A modern blog platform with article listings, reading experience optimization, and author profiles.",
    url: "https://blocks.mvp-subha.me",
    image: "/components.webp",
    category: "Blog",
    components: ["hero", "article-grid", "author-card", "newsletter", "footer"]
  },
  {
    id: "startup-site",
    name: "Startup Venture",
    description: "A professional startup website with team showcases, investor information, and product demonstrations.",
    url: "https://blocks.mvp-subha.me",
    image: "/gallery1.webp",
    category: "Startup",
    components: ["hero", "team", "features", "about", "contact"]
  },
  {
    id: "agency-website",
    name: "Digital Agency",
    description: "A creative agency website featuring service offerings, case studies, and client testimonials.",
    url: "https://blocks.mvp-subha.me",
    image: "/gallery2.webp",
    category: "Agency",
    components: ["hero", "services", "portfolio", "testimonials", "contact"]
  },
  {
    id: "fitness-app",
    name: "FitTrack Pro",
    description: "A fitness tracking application with workout plans, progress monitoring, and community features.",
    url: "https://blocks.mvp-subha.me",
    image: "/assets/fitness-hero/image.webp",
    category: "Health & Fitness",
    components: ["hero", "features", "pricing", "testimonials", "app-download"]
  }
];

export const categories = [
  "All",
  "SaaS",
  "E-commerce",
  "Portfolio",
  "Landing Page",
  "Blog",
  "Startup",
  "Agency",
  "Health & Fitness",
  "Component Library"
];

export function getShowcaseByCategory(category: string): ShowcaseItem[] {
  if (category === "All") {
    return showcaseData;
  }
  return showcaseData.filter(item => item.category === category);
}

export function getFeaturedShowcase(): ShowcaseItem[] {
  return showcaseData.filter(item => item.featured);
}
