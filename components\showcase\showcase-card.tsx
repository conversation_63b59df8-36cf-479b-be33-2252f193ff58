"use client";

import { motion } from "framer-motion";
import { ExternalLink, Star, Tag } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { ShowcaseItem } from "@/lib/showcase-data";
import { cn } from "@/lib/utils";

interface ShowcaseCardProps {
  item: ShowcaseItem;
  index: number;
}

export function ShowcaseCard({ item, index }: ShowcaseCardProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleClick = () => {
    window.open(item.url, "_blank", "noopener,noreferrer");
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="group relative"
    >
      <div
        onClick={handleClick}
        className={cn(
          "relative overflow-hidden rounded-xl border bg-card cursor-pointer transition-all duration-300",
          "hover:shadow-xl hover:shadow-primary/10 hover:-translate-y-1",
          "border-border/50 hover:border-primary/30",
          item.featured && "ring-2 ring-primary/20"
        )}
      >
        {/* Featured badge */}
        {item.featured && (
          <div className="absolute left-4 top-4 z-20 flex items-center gap-1 rounded-full bg-primary px-3 py-1 text-xs font-medium text-primary-foreground">
            <Star className="h-3 w-3 fill-current" />
            Featured
          </div>
        )}

        {/* Image container */}
        <div className="relative aspect-video overflow-hidden bg-muted">
          {/* Loading skeleton */}
          {!imageLoaded && !imageError && (
            <div className="absolute inset-0 animate-pulse bg-gradient-to-r from-muted via-muted/50 to-muted" />
          )}

          <Image
            src={item.image}
            alt={item.name}
            fill
            className={cn(
              "object-cover transition-all duration-300 group-hover:scale-105",
              imageLoaded ? "opacity-100" : "opacity-0"
            )}
            onLoad={() => setImageLoaded(true)}
            onError={() => {
              setImageError(true);
              setImageLoaded(true);
            }}
          />

          {/* Fallback for failed images */}
          {imageError && (
            <div className="absolute inset-0 flex items-center justify-center bg-muted">
              <div className="text-center">
                <div className="mb-2 text-2xl">🖼️</div>
                <p className="text-sm text-muted-foreground">Image not available</p>
              </div>
            </div>
          )}

          {/* Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

          {/* External link icon */}
          <div className="absolute right-4 top-4 z-10 rounded-full bg-background/80 p-2 opacity-0 backdrop-blur-sm transition-all duration-300 group-hover:opacity-100 group-hover:scale-110">
            <ExternalLink className="h-4 w-4 text-foreground" />
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Category badge */}
          <div className="mb-3 flex items-center gap-2">
            <div className="flex items-center gap-1 rounded-full bg-muted px-3 py-1 text-xs font-medium text-muted-foreground">
              <Tag className="h-3 w-3" />
              {item.category}
            </div>
          </div>

          {/* Title */}
          <h3 className="mb-2 text-xl font-semibold text-foreground group-hover:text-primary transition-colors duration-200">
            {item.name}
          </h3>

          {/* Description */}
          <p className="mb-4 text-sm text-muted-foreground line-clamp-3">
            {item.description}
          </p>

          {/* Components used */}
          {item.components && item.components.length > 0 && (
            <div className="mb-4">
              <p className="mb-2 text-xs font-medium text-muted-foreground uppercase tracking-wide">
                Components Used
              </p>
              <div className="flex flex-wrap gap-1">
                {item.components.slice(0, 3).map((component) => (
                  <span
                    key={component}
                    className="rounded-md bg-primary/10 px-2 py-1 text-xs font-medium text-primary"
                  >
                    {component}
                  </span>
                ))}
                {item.components.length > 3 && (
                  <span className="rounded-md bg-muted px-2 py-1 text-xs font-medium text-muted-foreground">
                    +{item.components.length - 3} more
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Visit button */}
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">
              Click to visit
            </span>
            <div className="flex items-center gap-1 text-xs font-medium text-primary opacity-0 transition-opacity duration-300 group-hover:opacity-100">
              Visit Site
              <ExternalLink className="h-3 w-3" />
            </div>
          </div>
        </div>

        {/* Hover effect overlay */}
        <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-primary/5 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
      </div>
    </motion.div>
  );
}
