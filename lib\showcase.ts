export interface ShowcaseItem {
  name: string;
  about: string;
  image: string;
  link: string;
}

export const showcaseData: ShowcaseItem[] = [
  {
    name: "MVPBlocks",
    about: "The official MVPBlocks website showcasing our complete component library with beautiful hero sections, feature grids, and interactive elements.",
    link: "https://blocks.mvp-subha.me",
    image: "/banner.webp"
  },
  {
    name: "SaaS Dashboard Pro",
    about: "A modern SaaS dashboard built with MVPBlocks components featuring analytics charts, user management, and responsive design.",
    link: "https://blocks.mvp-subha.me",
    image: "/assets/lunexa-db.png"
  },
  {
    name: "Modern Store",
    about: "An elegant e-commerce platform showcasing product grids, shopping cart functionality, and checkout flows using MVPBlocks.",
    link: "https://blocks.mvp-subha.me",
    image: "/assets/card-carousel/1.webp"
  },
  {
    name: "Creative Portfolio",
    about: "A stunning portfolio website for designers and developers featuring project showcases and contact forms.",
    link: "https://blocks.mvp-subha.me",
    image: "/assets/card-carousel/2.webp"
  },
  {
    name: "Product Launch",
    about: "A high-converting landing page for product launches with compelling CTAs, feature highlights, and social proof.",
    link: "https://blocks.mvp-subha.me",
    image: "/assets/card-carousel/3.webp"
  },
  {
    name: "Tech Blog Hub",
    about: "A modern blog platform with article listings, reading experience optimization, and author profiles.",
    link: "https://blocks.mvp-subha.me",
    image: "/components.webp"
  },
  {
    name: "Startup Venture",
    about: "A professional startup website with team showcases, investor information, and product demonstrations.",
    link: "https://blocks.mvp-subha.me",
    image: "/gallery1.webp"
  },
  {
    name: "Digital Agency",
    about: "A creative agency website featuring service offerings, case studies, and client testimonials.",
    link: "https://blocks.mvp-subha.me",
    image: "/gallery2.webp"
  },
  {
    name: "FitTrack Pro",
    about: "A fitness tracking application with workout plans, progress monitoring, and community features.",
    link: "https://blocks.mvp-subha.me",
    image: "/assets/fitness-hero/image.webp"
  }
];
