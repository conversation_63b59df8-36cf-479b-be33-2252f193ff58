"use client";

import { motion } from "framer-motion";
import { ExternalLink, Globe, Sparkles } from "lucide-react";
import Image from "next/image";
import { useTheme } from "next-themes";
import { ShowcaseGrid } from "@/components/showcase/showcase-grid";
import { Spotlight } from "@/components/ui/spotlight";

export default function ShowcasePage() {
  const { theme } = useTheme();

  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Background with metal-rose image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/metalrose.webp"
          alt="Background"
          fill
          className="object-cover opacity-5 dark:opacity-10"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-b from-background/80 via-background/90 to-background" />
      </div>

      {/* Spotlight effects */}
      <div className="absolute inset-0 z-10">
        <Spotlight />
      </div>
      <div className="absolute bottom-0 z-10 h-full w-full rotate-180">
        <Spotlight />
      </div>

      {/* Main content */}
      <div className="relative z-20 mx-auto max-w-6xl px-4 py-16 sm:px-6 lg:px-8">
        {/* Hero section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center"
        >
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-6 flex justify-center"
          >
            <div className="group relative rounded-full border border-primary/20 bg-background/50 px-6 py-2 backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:border-primary/40 hover:shadow-lg hover:shadow-primary/10">
              <div className="absolute inset-x-0 -top-px mx-auto h-0.5 w-1/2 bg-gradient-to-r from-transparent via-primary to-transparent shadow-2xl transition-all duration-500 group-hover:w-3/4" />
              <div className="flex items-center gap-2 text-sm font-medium text-primary">
                <Sparkles className="h-4 w-4" />
                Showcase
              </div>
            </div>
          </motion.div>

          {/* Title */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-6 bg-gradient-to-b from-foreground via-foreground/90 to-foreground/70 bg-clip-text text-4xl font-bold tracking-tight text-transparent sm:text-5xl lg:text-6xl"
          >
            Built with{" "}
            <span className="bg-gradient-to-r from-primary via-primary/90 to-primary/80 bg-clip-text text-transparent">
              MVPBlocks
            </span>
          </motion.h1>

          {/* Description */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="mx-auto mb-12 max-w-3xl text-lg text-muted-foreground sm:text-xl"
          >
            Discover amazing websites and applications built by our community using MVPBlocks components.
            Get inspired and see what&apos;s possible with our component library.
          </motion.p>

          {/* CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mb-16 flex flex-col items-center gap-4 sm:flex-row sm:justify-center"
          >
            <a
              href="https://github.com/subhadeeproy3902/mvpblocks"
              target="_blank"
              rel="noopener noreferrer"
              className="group inline-flex items-center gap-2 rounded-lg bg-primary px-6 py-3 text-sm font-medium text-primary-foreground transition-all duration-200 hover:bg-primary/90 hover:scale-105 hover:shadow-lg hover:shadow-primary/25"
            >
              <Globe className="h-4 w-4" />
              Submit Your Site
              <ExternalLink className="h-4 w-4 transition-transform group-hover:translate-x-0.5 group-hover:-translate-y-0.5" />
            </a>
            <span className="text-sm text-muted-foreground">
              Share your creation with the community
            </span>
          </motion.div>
        </motion.div>

        {/* Stats section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="mb-16 grid grid-cols-2 gap-6 sm:grid-cols-4"
        >
          {[
            { label: "Websites Built", value: "500+" },
            { label: "Components Used", value: "50+" },
            { label: "Happy Developers", value: "1000+" },
            { label: "Countries", value: "25+" },
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
              className="text-center"
            >
              <div className="mb-2 text-2xl font-bold text-primary sm:text-3xl">
                {stat.value}
              </div>
              <div className="text-sm text-muted-foreground">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Showcase grid */}
        <ShowcaseGrid />
      </div>
    </div>
  );
}
